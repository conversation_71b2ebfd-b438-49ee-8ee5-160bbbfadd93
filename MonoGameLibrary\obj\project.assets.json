{"version": 3, "targets": {"net8.0": {"MonoGame.Framework.DesktopGL/3.8.4": {"type": "package", "dependencies": {"MonoGame.Library.OpenAL": "*********", "MonoGame.Library.SDL": "********", "NVorbis": "0.10.4"}, "compile": {"lib/net8.0/MonoGame.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/MonoGame.Framework.dll": {"related": ".xml"}}, "build": {"build/MonoGame.Framework.DesktopGL.targets": {}}}, "MonoGame.Library.OpenAL/*********": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/libopenal.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libopenal.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libopenal.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libopenal.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/ios-arm64/native/libopenal.dylib": {"assetType": "native", "rid": "ios-arm64"}, "runtimes/iossimulator-arm64/native/libopenal.dylib": {"assetType": "native", "rid": "iossimulator-arm64"}, "runtimes/iossimulator-x64/native/libopenal.dylib": {"assetType": "native", "rid": "iossimulator-x64"}, "runtimes/linux-x64/native/libopenal.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libopenal.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-x64/native/openal.dll": {"assetType": "native", "rid": "win-x64"}}}, "MonoGame.Library.SDL/********": {"type": "package", "runtimeTargets": {"runtimes/linux-x64/native/libSDL2-2.0.so.0": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libSDL2-2.0.0.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-x64/native/SDL2.dll": {"assetType": "native", "rid": "win-x64"}}}, "NVorbis/0.10.4": {"type": "package", "dependencies": {"System.Memory": "4.5.3", "System.ValueTuple": "4.5.0"}, "compile": {"lib/netstandard2.0/NVorbis.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NVorbis.dll": {"related": ".xml"}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}}}, "libraries": {"MonoGame.Framework.DesktopGL/3.8.4": {"sha512": "2WR/vPNPcmQ9h4EhMkfpUfMW0Krm6pX0ElkZwSRI1IUecPuzLeXt0i9NRwGxZBUdcNdNxACCmswrxSf4B95Obg==", "type": "package", "path": "monogame.framework.desktopgl/3.8.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README-packages.md", "build/MonoGame.Framework.DesktopGL.targets", "lib/net8.0/MonoGame.Framework.dll", "lib/net8.0/MonoGame.Framework.xml", "monogame.framework.desktopgl.3.8.4.nupkg.sha512", "monogame.framework.desktopgl.nuspec"]}, "MonoGame.Library.OpenAL/*********": {"sha512": "4/F4FFyt7OYhtEmgi/Qmv01eC6GfQ9dkIPsymgkwJ/tV7PewmWrHg/LvTKjd6R+5j/tlO8JkjsEb0Aje6bxfxw==", "type": "package", "path": "monogame.library.openal/*********", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE", "README.md", "monogame.library.openal.*********.nupkg.sha512", "monogame.library.openal.nuspec", "runtimes/android-arm/native/libopenal.so", "runtimes/android-arm64/native/libopenal.so", "runtimes/android-x64/native/libopenal.so", "runtimes/android-x86/native/libopenal.so", "runtimes/ios-arm64/native/libopenal.dylib", "runtimes/iossimulator-arm64/native/libopenal.dylib", "runtimes/iossimulator-x64/native/libopenal.dylib", "runtimes/linux-x64/native/libopenal.so", "runtimes/osx/native/libopenal.dylib", "runtimes/win-x64/native/openal.dll"]}, "MonoGame.Library.SDL/********": {"sha512": "T4E2ppGlSTC2L9US1rxtdg3qTbarRzNId31xZoumUW9cf9Nq8nRQPMu9GzvZGrhfSySf0+UWPEj1rlicps+P/w==", "type": "package", "path": "monogame.library.sdl/********", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.txt", "README.md", "monogame.library.sdl.********.nupkg.sha512", "monogame.library.sdl.nuspec", "runtimes/linux-x64/native/libSDL2-2.0.so.0", "runtimes/osx/native/libSDL2-2.0.0.dylib", "runtimes/win-x64/native/SDL2.dll"]}, "NVorbis/0.10.4": {"sha512": "WYnil3DhQHzjCY0dM9I2B3r1vWip90AOuQd25KE4NrjPQBg0tBJFluRLm5YPnO5ZLDmwrfosY8jCQGQRmWI/Pg==", "type": "package", "path": "nvorbis/0.10.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net45/NVorbis.dll", "lib/net45/NVorbis.xml", "lib/netstandard2.0/NVorbis.dll", "lib/netstandard2.0/NVorbis.xml", "nvorbis.0.10.4.nupkg.sha512", "nvorbis.nuspec"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net8.0": ["MonoGame.Framework.DesktopGL >= 3.8.*"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\MyGame\\MonoGameLibrary\\MonoGameLibrary.csproj", "projectName": "MonoGameLibrary", "projectPath": "C:\\Users\\<USER>\\MyGame\\MonoGameLibrary\\MonoGameLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\MyGame\\MonoGameLibrary\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MonoGame.Framework.DesktopGL": {"suppressParent": "All", "target": "Package", "version": "[3.8.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}