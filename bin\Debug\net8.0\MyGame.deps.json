{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"MyGame/1.0.0": {"dependencies": {"MonoGame.Content.Builder.Task": "3.8.4", "MonoGame.Framework.DesktopGL": "3.8.4"}, "runtime": {"MyGame.dll": {}}}, "MonoGame.Content.Builder.Task/3.8.4": {}, "MonoGame.Framework.DesktopGL/3.8.4": {"dependencies": {"MonoGame.Library.OpenAL": "*********", "MonoGame.Library.SDL": "********", "NVorbis": "0.10.4"}, "runtime": {"lib/net8.0/MonoGame.Framework.dll": {"assemblyVersion": "3.8.4.0", "fileVersion": "3.8.4.0"}}}, "MonoGame.Library.OpenAL/*********": {"runtimeTargets": {"runtimes/android-arm/native/libopenal.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libopenal.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libopenal.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libopenal.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios-arm64/native/libopenal.dylib": {"rid": "ios-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/iossimulator-arm64/native/libopenal.dylib": {"rid": "iossimulator-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/iossimulator-x64/native/libopenal.dylib": {"rid": "iossimulator-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libopenal.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libopenal.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/openal.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.23.1.0"}}}, "MonoGame.Library.SDL/********": {"runtimeTargets": {"runtimes/linux-x64/native/libSDL2-2.0.so.0": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libSDL2-2.0.0.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SDL2.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.32.2.0"}}}, "NVorbis/0.10.4": {"dependencies": {"System.Memory": "4.5.3", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/NVorbis.dll": {"assemblyVersion": "0.10.4.0", "fileVersion": "0.10.4.0"}}}, "System.Memory/4.5.3": {}, "System.ValueTuple/4.5.0": {}}}, "libraries": {"MyGame/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MonoGame.Content.Builder.Task/3.8.4": {"type": "package", "serviceable": true, "sha512": "sha512-Oc/tp6liQ34BkBCrdQO23JDf1Z8r11kKeL9oXSlEfUiRSQYs7C0TYaKIjhfYGjSRaV7JaFIisLDToWpnf7ztKg==", "path": "monogame.content.builder.task/3.8.4", "hashPath": "monogame.content.builder.task.3.8.4.nupkg.sha512"}, "MonoGame.Framework.DesktopGL/3.8.4": {"type": "package", "serviceable": true, "sha512": "sha512-2WR/vPNPcmQ9h4EhMkfpUfMW0Krm6pX0ElkZwSRI1IUecPuzLeXt0i9NRwGxZBUdcNdNxACCmswrxSf4B95Obg==", "path": "monogame.framework.desktopgl/3.8.4", "hashPath": "monogame.framework.desktopgl.3.8.4.nupkg.sha512"}, "MonoGame.Library.OpenAL/*********": {"type": "package", "serviceable": true, "sha512": "sha512-4/F4FFyt7OYhtEmgi/Qmv01eC6GfQ9dkIPsymgkwJ/tV7PewmWrHg/LvTKjd6R+5j/tlO8JkjsEb0Aje6bxfxw==", "path": "monogame.library.openal/*********", "hashPath": "monogame.library.openal.*********.nupkg.sha512"}, "MonoGame.Library.SDL/********": {"type": "package", "serviceable": true, "sha512": "sha512-T4E2ppGlSTC2L9US1rxtdg3qTbarRzNId31xZoumUW9cf9Nq8nRQPMu9GzvZGrhfSySf0+UWPEj1rlicps+P/w==", "path": "monogame.library.sdl/********", "hashPath": "monogame.library.sdl.********.nupkg.sha512"}, "NVorbis/0.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-WYnil3DhQHzjCY0dM9I2B3r1vWip90AOuQd25KE4NrjPQBg0tBJFluRLm5YPnO5ZLDmwrfosY8jCQGQRmWI/Pg==", "path": "nvorbis/0.10.4", "hashPath": "nvorbis.0.10.4.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}}}